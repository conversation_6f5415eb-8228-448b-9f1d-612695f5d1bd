import React, { useState, useEffect } from 'react';
import {
  Zap,
  CheckCircle,
  XCircle,
  AlertCircle,
  Monitor,
  Settings,
  Shield,
  Database,
  RefreshCw,
  Download,
  Copy,
  ExternalLink,
  Loader2,
  Clock,
  Users,
  Globe,
  ArrowUpRight
} from 'lucide-react';
import { apiService } from '../../utils/api';
import { useAuth } from '../auth/AuthContext';
import { IntegrationHub } from '../integrations/IntegrationHub';
import ComposioConnect from '../composio/ComposioConnect';
import { PermissionManagement } from '../permissions/PermissionManagement';


interface IntegrationStatus {
  id: string;
  name: string;
  status: 'connected' | 'available' | 'error' | 'pending';
  lastSync?: Date;
  userCount?: number;
  icon: React.ReactNode;
  category: 'google' | 'productivity' | 'communication';
}

interface MCPStatus {
  proxy_status: 'online' | 'offline' | 'degraded';
  connected_tools: number;
  active_sessions: number;
  last_sync: string;
}

export function DashboardOverview() {
  const { employee } = useAuth();
  const [integrations, setIntegrations] = useState<IntegrationStatus[]>([]);
  const [mcpStatus, setMcpStatus] = useState<MCPStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mcpConfig, setMcpConfig] = useState<any>(null);
  const [mcpLoading, setMcpLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'integrations' | 'mcp-config' | 'access-control'>('overview');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [connectionHealth, setConnectionHealth] = useState<{[key: string]: 'healthy' | 'degraded' | 'error'}>({});



  // Initialize integration statuses
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Auto-refresh monitoring
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadDashboardData();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh]);

  // Monitor connection health
  const checkConnectionHealth = async () => {
    const health: {[key: string]: 'healthy' | 'degraded' | 'error'} = {};

    try {
      // Check OAuth service health
      const oauthHealth = await apiService.checkOAuthHealth();
      health.oauth = oauthHealth.status === 'healthy' ? 'healthy' :
                    oauthHealth.status === 'degraded' ? 'degraded' : 'error';
    } catch {
      health.oauth = 'error';
    }

    try {
      // Check MCP proxy health
      const mcpHealth = await apiService.checkMCPHealth();
      health.mcp = mcpHealth.status === 'Smart MCP Proxy running' ? 'healthy' : 'error';
    } catch {
      health.mcp = 'error';
    }

    setConnectionHealth(health);
  };

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Load OAuth status, MCP status, and health checks in parallel
      const [oauthResult, mcpResult] = await Promise.all([
        apiService.getOAuthStatus().catch(() => ({ connected: false, scopes: [] })),
        apiService.checkMCPHealth().catch(() => ({ status: 'offline', n8n_connection: null }))
      ]);

      // Update connection health monitoring
      await checkConnectionHealth();
      setLastUpdate(new Date());

      // Initialize integrations with current status
      const initialIntegrations: IntegrationStatus[] = [
        {
          id: 'gmail',
          name: 'Gmail',
          status: 'available',
          icon: <div className="w-6 h-6 bg-red-500 rounded flex items-center justify-center text-white text-xs font-bold">G</div>,
          category: 'google'
        },
        {
          id: 'drive',
          name: 'Google Drive',
          status: 'available',
          icon: <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center text-white text-xs font-bold">D</div>,
          category: 'google'
        },
        {
          id: 'calendar',
          name: 'Google Calendar',
          status: 'available',
          icon: <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center text-white text-xs font-bold">C</div>,
          category: 'google'
        },
        {
          id: 'slack',
          name: 'Slack',
          status: 'available',
          icon: <div className="w-6 h-6 bg-purple-500 rounded flex items-center justify-center text-white text-xs font-bold">S</div>,
          category: 'communication'
        },
        {
          id: 'notion',
          name: 'Notion',
          status: 'available',
          icon: <div className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center text-white text-xs font-bold">N</div>,
          category: 'productivity'
        },
        {
          id: 'airtable',
          name: 'Airtable',
          status: 'available',
          icon: <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center text-white text-xs font-bold">A</div>,
          category: 'productivity'
        }
      ];

      // Update Google services status based on OAuth
      if (oauthResult.connected && oauthResult.scopes && Array.isArray(oauthResult.scopes)) {
        initialIntegrations.forEach(integration => {
          if (integration.category === 'google') {
            const isConnected = oauthResult.scopes!.some((scope: string) =>
              scope.includes(integration.id === 'drive' ? 'drive' : integration.id)
            );
            if (isConnected) {
              integration.status = 'connected';
              integration.lastSync = new Date();
              integration.userCount = 1;
            }
          }
        });
      }

      setIntegrations(initialIntegrations);

      // Set MCP status
      if (mcpResult.status === 'Smart MCP Proxy running') {
        setMcpStatus({
          proxy_status: 'online',
          connected_tools: mcpResult.n8n_connection ? 1 : 0,
          active_sessions: 1,
          last_sync: new Date().toISOString()
        });
      } else {
        setMcpStatus({
          proxy_status: 'degraded',
          connected_tools: 0,
          active_sessions: 0,
          last_sync: new Date().toISOString()
        });
      }

    } catch (error) {
      setMcpStatus({
        proxy_status: 'offline',
        connected_tools: 0,
        active_sessions: 0,
        last_sync: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateMCPConfig = async () => {
    try {
      setMcpLoading(true);
      // Check if we have employee data from context
      let userEmail: string;
      if (typeof employee === 'string') {
        userEmail = employee;
      } else if (employee?.email) {
        userEmail = employee.email;
      } else {
        const errorMsg = 'No employee data available. Please refresh the page and try again.';
        alert(errorMsg);
        throw new Error(errorMsg);
      }

      const result = await apiService.generateMCPConfig();

      if (result.success) {
        setMcpConfig(result);
        // Show success message
        if (typeof window !== 'undefined') {
          alert('Claude Desktop configuration generated successfully! Check the Configuration Preview section below.');
        }
      } else {
        if (typeof window !== 'undefined') {
          alert('Failed to generate MCP configuration. Please try again.');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      if (typeof window !== 'undefined') {
        alert(`Failed to generate MCP configuration: ${errorMessage}`);
      }
    } finally {
      setMcpLoading(false);
    }
  };

  const downloadMCPConfig = () => {
    if (!mcpConfig) return;

    const configContent = JSON.stringify(mcpConfig.config, null, 2);
    const blob = new Blob([configContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'claude_desktop_config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const copyMCPConfig = () => {
    if (!mcpConfig) return;

    const configContent = JSON.stringify(mcpConfig.config, null, 2);
    navigator.clipboard.writeText(configContent).then(() => {
      if (typeof window !== 'undefined') {
        alert('Configuration copied to clipboard!');
      }
    }).catch((error) => {
      console.error('Failed to copy to clipboard:', error);
      if (typeof window !== 'undefined') {
        alert('Failed to copy to clipboard. Please try again.');
      }
    });
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'pending': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-400" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-400" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Real-time Status Header */}
      <div className="bg-gray-800 rounded-xl p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionHealth.oauth === 'healthy' ? 'bg-green-400' :
                connectionHealth.oauth === 'degraded' ? 'bg-yellow-400' : 'bg-red-400'
              }`}></div>
              <span className="text-gray-300 text-sm">OAuth Service</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionHealth.mcp === 'healthy' ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span className="text-gray-300 text-sm">MCP Proxy</span>
            </div>
            <div className="text-gray-400 text-sm">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                autoRefresh
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
            </button>
            <button
              onClick={loadDashboardData}
              disabled={isLoading}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors disabled:opacity-50 flex items-center space-x-1"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* MCP Proxy Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-600/20 to-cyan-600/20 border border-blue-500/30 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-300 text-sm">MCP Proxy Status</p>
              <p className="text-2xl font-bold text-white capitalize">
                {mcpStatus?.proxy_status || 'Loading...'}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-600/30 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Connected Tools</p>
              <p className="text-2xl font-bold text-white">{mcpStatus?.connected_tools || 0}</p>
            </div>
            <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
              <Zap className="w-6 h-6 text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Integrations</p>
              <p className="text-2xl font-bold text-white">
                {integrations.filter(i => i.status === 'connected').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Sessions</p>
              <p className="text-2xl font-bold text-white">{mcpStatus?.active_sessions || 0}</p>
            </div>
            <div className="w-12 h-12 bg-cyan-600/20 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-cyan-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-800 rounded-xl p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: Monitor },
            { id: 'integrations', label: 'Integrations', icon: Zap },
            { id: 'mcp-config', label: 'Claude Desktop', icon: Download },
            { id: 'access-control', label: 'Access Control', icon: Shield }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Key Intelligence Insights */}
          <div className="lg:col-span-2 bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Key Intelligence Insights</h3>

            <div className="space-y-4">
              <div className="border border-green-600/30 bg-green-600/10 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-green-400 font-medium">Growth Opportunity Identified</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  73% increase in AI/automation questions from clients suggests strong demand for technology consulting services.
                </p>
                <p className="text-gray-400 text-xs">
                  <strong>Recommended Action:</strong> Develop "Digital CFO Services" package and schedule technology discussions with top 12 clients who expressed interest.
                </p>
              </div>

              <div className="border border-blue-600/30 bg-blue-600/10 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span className="text-blue-400 font-medium">Client Satisfaction Pattern</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  Clients with monthly check-ins show 85% higher satisfaction scores and 60% better retention rates.
                </p>
                <p className="text-gray-400 text-xs">
                  <strong>Insight:</strong> Consider standardizing monthly touchpoints for all clients and creating structured check-in templates.
                </p>
              </div>

              <div className="border border-orange-600/30 bg-orange-600/10 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                  <span className="text-orange-400 font-medium">Response Time Impact</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  Average response time to client questions improved from 6.1 to 4.2 hours, correlating with 23% increase in client satisfaction.
                </p>
                <p className="text-gray-400 text-xs">
                  <strong>Goal:</strong> Target sub-3 hour response time could further improve engagement and differentiate from competitors.
                </p>
              </div>
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            {/* Top Opportunities */}
            <div className="bg-gray-800 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                Top Opportunities
              </h4>
              <div className="space-y-3">
                <div className="text-sm">
                  <div className="flex items-center space-x-2 mb-1">
                    <ArrowUpRight className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400">Technology adoption consulting for 12 interested clients</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center space-x-2 mb-1">
                    <ArrowUpRight className="w-4 h-4 text-green-400" />
                    <span className="text-green-400">Operational scaling guidance for growth-stage companies</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center space-x-2 mb-1">
                    <ArrowUpRight className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400">Fractional CFO expansion into 3 new industry verticals</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center space-x-2 mb-1">
                    <ArrowUpRight className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400">Automated reporting services for recurring revenue</span>
                  </div>
                </div>
              </div>
            </div>

            {/* This Week's Trends */}
            <div className="bg-gray-800 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                <div className="w-2 h-2 bg-cyan-400 rounded-full mr-2"></div>
                This Week's Trends
              </h4>
              <div className="space-y-3">
                <div className="text-sm">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-gray-300">Cash flow optimization requests</span>
                    <span className="text-red-400">+45% vs last week</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-gray-300">Strategic guidance requests outpacing tactical work</span>
                    <span className="text-green-400">3:1</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-gray-300">Client meeting duration increased</span>
                    <span className="text-green-400">18% (positive engagement)</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-gray-300">Follow-up questions after deliverables</span>
                    <span className="text-orange-400">up 32%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Items */}
            <div className="bg-gray-800 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                <div className="w-2 h-2 bg-orange-400 rounded-full mr-2"></div>
                Action Items
              </h4>
              <div className="space-y-3">
                <div className="text-sm">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    <span className="text-gray-300">Schedule tech consultations with TechStart, Devlin, Manufacturing Co</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Integrations Tab */}
      {activeTab === 'integrations' && (
        <div className="space-y-6">
          {/* Connection Status Overview */}
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Connection Status Overview</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-900 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Google Services</span>
                  <div className="flex items-center space-x-2">
                    {integrations.filter(i => i.category === 'google' && i.status === 'connected').length > 0 ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-green-400 text-sm">Connected</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="w-4 h-4 text-yellow-400" />
                        <span className="text-yellow-400 text-sm">Available</span>
                      </>
                    )}
                  </div>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {integrations.filter(i => i.category === 'google' && i.status === 'connected').length} of {integrations.filter(i => i.category === 'google').length} connected
                </div>
              </div>

              <div className="bg-gray-900 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Productivity Tools</span>
                  <div className="flex items-center space-x-2">
                    {integrations.filter(i => i.category === 'productivity' && i.status === 'connected').length > 0 ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-green-400 text-sm">Connected</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-400 text-sm">Available</span>
                      </>
                    )}
                  </div>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {integrations.filter(i => i.category === 'productivity' && i.status === 'connected').length} of {integrations.filter(i => i.category === 'productivity').length} connected
                </div>
              </div>

              <div className="bg-gray-900 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Communication</span>
                  <div className="flex items-center space-x-2">
                    {integrations.filter(i => i.category === 'communication' && i.status === 'connected').length > 0 ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-green-400 text-sm">Connected</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-400 text-sm">Available</span>
                      </>
                    )}
                  </div>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {integrations.filter(i => i.category === 'communication' && i.status === 'connected').length} of {integrations.filter(i => i.category === 'communication').length} connected
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Integration Management</h3>
            <p className="text-gray-400 mb-6">Connect and manage your business productivity tools and personal services.</p>

            {/* Google Services Quick Connect */}
            <div className="mb-8">
              <h4 className="text-md font-semibold text-white mb-4">Google Services</h4>
              <ComposioConnect onConnectionUpdate={loadDashboardData} />
            </div>

            {/* Full Integration Hub */}
            <IntegrationHub />
          </div>
        </div>
      )}

      {/* Debug: Show current tab */}
      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3 mb-4">
        <p className="text-yellow-300 text-sm">
          🔍 Debug: Current tab = "{activeTab}", MCP Config tab visible = {activeTab === 'mcp-config' ? 'YES' : 'NO'}
        </p>
      </div>

      {/* MCP Configuration Tab */}
      {activeTab === 'mcp-config' && (
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Claude Desktop MCP Configuration</h3>
            <p className="text-gray-400 mb-6">Generate and download your Claude Desktop configuration to access all your connected tools.</p>

            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <button
                  onClick={generateMCPConfig}
                  disabled={mcpLoading}
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  {mcpLoading ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <RefreshCw className="w-5 h-5" />
                  )}
                  <span>{mcpLoading ? 'Generating...' : 'Generate Configuration'}</span>
                </button>

                {/* Test button for debugging */}
                <button
                  onClick={testSetMCPConfig}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
                >
                  🧪 Test Config
                </button>

                {mcpConfig && (
                  <>
                    <button
                      onClick={downloadMCPConfig}
                      className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
                    >
                      <Download className="w-5 h-5" />
                      <span>Download Config</span>
                    </button>

                    <button
                      onClick={copyMCPConfig}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
                    >
                      <Copy className="w-5 h-5" />
                      <span>Copy to Clipboard</span>
                    </button>
                  </>
                )}
              </div>

              {/* Debug section - always show if mcpConfig exists */}
              {mcpConfig && (
                <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-4">
                  <h4 className="text-red-300 font-medium mb-2">Debug: Raw mcpConfig Data</h4>
                  <pre className="text-red-200 text-xs overflow-x-auto">
                    {JSON.stringify(mcpConfig, null, 2)}
                  </pre>
                </div>
              )}

              {mcpConfig && (
                <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-2">Configuration Preview</h4>
                  <div className="text-xs text-gray-500 mb-2">
                    Debug: mcpConfig exists = {mcpConfig ? 'true' : 'false'},
                    config property exists = {mcpConfig?.config ? 'true' : 'false'}
                  </div>
                  <pre className="text-gray-300 text-sm overflow-x-auto">
                    {JSON.stringify(mcpConfig.config, null, 2)}
                  </pre>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <ExternalLink className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="text-blue-300 font-medium mb-1">Setup Instructions</h4>
                      <p className="text-blue-200 text-sm">
                        1. Generate your configuration above<br/>
                        2. Download the config file<br/>
                        3. Download the MCP server file<br/>
                        4. Place both files in your Claude Desktop folder<br/>
                        5. Restart Claude Desktop to load your tools
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Download className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="text-green-300 font-medium mb-1">MCP Server File</h4>
                      <p className="text-green-200 text-sm mb-2">
                        Download the Uru MCP server file for Claude Desktop
                      </p>
                      <button
                        onClick={() => {
                          const serverUrl = `${window.location.origin}/api/download/uru-claude-server.js`;
                          window.open(serverUrl, '_blank');
                        }}
                        className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors flex items-center space-x-1"
                      >
                        <Download className="w-3 h-3" />
                        <span>Download Server</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Access Control Tab */}
      {activeTab === 'access-control' && (
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Access Control Settings</h3>
            <p className="text-gray-400 mb-6">Manage team member permissions for company-specific tools and data sources.</p>

            <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-4 mb-6">
              <div className="flex items-start space-x-3">
                <Shield className="w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-orange-300 font-medium mb-1">Enterprise Feature</h4>
                  <p className="text-orange-200 text-sm">
                    Access control settings allow you to manage permissions for company-specific tools like transcript search,
                    client databases, knowledge bases, and RAG sources. Personal productivity tools (Gmail, Drive, Calendar)
                    are automatically available to authenticated users.
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-gray-900 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">Company Tools</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Transcript Search</span>
                    <div className="w-10 h-6 bg-green-600 rounded-full flex items-center justify-end px-1">
                      <div className="w-4 h-4 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Client Database</span>
                    <div className="w-10 h-6 bg-green-600 rounded-full flex items-center justify-end px-1">
                      <div className="w-4 h-4 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Knowledge Base</span>
                    <div className="w-10 h-6 bg-gray-600 rounded-full flex items-center justify-start px-1">
                      <div className="w-4 h-4 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">RAG Sources</span>
                    <div className="w-10 h-6 bg-green-600 rounded-full flex items-center justify-end px-1">
                      <div className="w-4 h-4 bg-white rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-900 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">Team Access</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                      JM
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">Jackson Moss</p>
                      <p className="text-gray-400 text-xs">Admin • Full Access</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                      TM
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">Team Member</p>
                      <p className="text-gray-400 text-xs">Member • Limited Access</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Permission Management */}
            <div className="bg-gray-900 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4">Detailed Permission Management</h4>
              <PermissionManagement />
            </div>
          </div>
        </div>
      )}

      {/* Test Tab */}
      {activeTab === 'test' && (
        <div className="space-y-6">
          <DashboardTest />

          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Manual Testing Checklist</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-3">Integration Features</h4>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li>✓ OAuth connection status monitoring</li>
                  <li>✓ Google services toggle switches</li>
                  <li>✓ Integration hub with multiple providers</li>
                  <li>✓ Real-time connection health indicators</li>
                  <li>✓ Auto-refresh functionality</li>
                </ul>
              </div>

              <div>
                <h4 className="text-white font-medium mb-3">MCP Configuration</h4>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li>✓ Claude Desktop config generation</li>
                  <li>✓ Long-lived token creation</li>
                  <li>✓ Configuration file download</li>
                  <li>✓ MCP server file download</li>
                  <li>✓ Copy to clipboard functionality</li>
                </ul>
              </div>

              <div>
                <h4 className="text-white font-medium mb-3">Access Control</h4>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li>✓ Enterprise permission management</li>
                  <li>✓ Team member access control</li>
                  <li>✓ Company tool permissions</li>
                  <li>✓ Role-based access settings</li>
                  <li>✓ Detailed permission interface</li>
                </ul>
              </div>

              <div>
                <h4 className="text-white font-medium mb-3">UI/UX Features</h4>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li>✓ Blue gradient theme consistency</li>
                  <li>✓ Rounded-xl card layouts</li>
                  <li>✓ Inter font implementation</li>
                  <li>✓ Responsive design</li>
                  <li>✓ Tab-based navigation</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
