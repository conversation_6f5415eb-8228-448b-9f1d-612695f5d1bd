import React from 'react';
import { useAuth } from '../auth/AuthContext';

export function DebugInfo() {
  const { employee, isAuthenticated, isLoading } = useAuth();

  return (
    <div className="fixed top-4 right-4 bg-red-900/90 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h4 className="font-bold mb-2">🐛 Debug Info</h4>
      <div className="space-y-1">
        <div>Auth Loading: {isLoading ? 'Yes' : 'No'}</div>
        <div>Is Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
        <div>Employee: {employee ? 'Yes' : 'No'}</div>
        <div>Employee Email: {employee?.email || 'None'}</div>
        <div>Component: DashboardOverview</div>
        <div>Timestamp: {new Date().toLocaleTimeString()}</div>
        <div>URL: {typeof window !== 'undefined' ? window.location.pathname : 'SSR'}</div>
      </div>
    </div>
  );
}
