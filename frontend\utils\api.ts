// frontend/utils/api.ts
// API service for connecting to OAuth service and MCP proxy

import { AppError, handleError } from './errorHandler';

interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface Employee {
  id: string;
  email: string;
  name: string;
  role: string;
  workspace: {
    id: string;
    name: string;
    slug: string;
  };
}

interface AuthResponse {
  employee: Employee;
  access_token: string;
  token?: string; // For backward compatibility
  expires_at: string;
}

interface OAuthStatus {
  connected: boolean;
  expired?: boolean;
  scopes?: string[];
  expires_at?: string;
  user_info?: {
    email?: string;
    name?: string;
  };
  needs_refresh?: boolean;
  message?: string;
}

interface MCPTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: any;
  };
}

interface MCPToolResult {
  success: boolean;
  tool_name: string;
  result: any;
  timestamp: string;
}

class ApiService {
  private oauthBaseUrl: string | null = null;
  private authBaseUrl: string | null = null;
  private integrationsBaseUrl: string | null = null;
  private mcpBaseUrl: string | null = null;
  private token: string | null = null;
  private initialized = false;

  private initialize() {
    if (this.initialized) return;

    // Always use new architecture
    console.log('🏗️ Frontend Architecture: NEW (auth-service + integration-service)');

    // Enhanced environment detection for Elestio deployment
    const isProduction = process.env.NODE_ENV === 'production' ||
                        (typeof window !== 'undefined' && window.location.hostname.includes('uruenterprises.com'));

    // Get environment variables with fallbacks
    const authUrl = process.env.NEXT_PUBLIC_AUTH_URL;
    const integrationsUrl = process.env.NEXT_PUBLIC_INTEGRATIONS_URL;
    const mcpUrl = process.env.NEXT_PUBLIC_MCP_URL;

    if (isProduction) {
      // Production URL configuration - New architecture only
      this.authBaseUrl = authUrl || 'https://auth.uruenterprises.com';
      this.integrationsBaseUrl = integrationsUrl || 'https://integrations.uruenterprises.com';
      this.oauthBaseUrl = this.authBaseUrl; // For backward compatibility
      this.mcpBaseUrl = mcpUrl || 'https://mcp.uruenterprises.com';

      console.log('✅ Using NEW architecture production URLs:', {
        authBaseUrl: this.authBaseUrl,
        integrationsBaseUrl: this.integrationsBaseUrl,
        mcpBaseUrl: this.mcpBaseUrl
      });
    } else {
      // Development defaults to localhost - New architecture only
      this.authBaseUrl = authUrl || 'http://localhost:8003';
      this.integrationsBaseUrl = integrationsUrl || 'http://localhost:8002';
      this.oauthBaseUrl = this.authBaseUrl; // For backward compatibility
      this.mcpBaseUrl = mcpUrl || 'http://localhost:3001';

      console.log('🔧 Using NEW architecture development URLs:', {
        authBaseUrl: this.authBaseUrl,
        integrationsBaseUrl: this.integrationsBaseUrl,
        mcpBaseUrl: this.mcpBaseUrl
      });
    }

    // Enhanced debug logging for production troubleshooting
    if (typeof window !== 'undefined') {
      console.log('🌍 API Service Configuration:', {
        environment: isProduction ? 'production' : 'development',
        hostname: window.location.hostname,
        protocol: window.location.protocol,
        port: window.location.port,
        oauthBaseUrl: this.oauthBaseUrl,
        mcpBaseUrl: this.mcpBaseUrl,
        envVars: {
          NODE_ENV: process.env.NODE_ENV,
          NEXT_PUBLIC_AUTH_URL: authUrl ? 'set' : 'not set',
          NEXT_PUBLIC_INTEGRATIONS_URL: integrationsUrl ? 'set' : 'not set',
          NEXT_PUBLIC_MCP_URL: mcpUrl ? 'set' : 'not set',
          NEXT_PUBLIC_USE_NEW_ARCHITECTURE: 'true'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Try to get token from localStorage on client side
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
      if (this.token) {
        console.log('🔑 Found existing auth token in localStorage', {
          tokenLength: this.token.length,
          tokenPreview: `${this.token.substring(0, 20)}...`,
          tokenEnd: `...${this.token.substring(this.token.length - 10)}`
        });
      } else {
        console.log('❌ No auth token found in localStorage');
        // Check what's actually in localStorage
        const allKeys = Object.keys(localStorage);
        console.log('🔍 All localStorage keys:', allKeys);
      }
    }

    this.initialized = true;
  }

  private getAuthHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    
    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text();

      // Enhanced error logging for production debugging
      const errorDetails = {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
        errorText: errorText,
        timestamp: new Date().toISOString()
      };

      console.error('API Request Failed:', errorDetails);

      // Try to parse error as JSON for better error messages
      let errorMessage = `API Error: ${response.status} - ${errorText}`;
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.detail) {
          // Handle both string and object detail formats
          if (typeof errorJson.detail === 'string') {
            errorMessage = errorJson.detail;
          } else if (typeof errorJson.detail === 'object' && errorJson.detail.message) {
            errorMessage = errorJson.detail.message;
          } else if (typeof errorJson.detail === 'object' && errorJson.detail.error) {
            errorMessage = errorJson.detail.error;
          } else {
            errorMessage = JSON.stringify(errorJson.detail);
          }
        } else if (errorJson.message) {
          errorMessage = errorJson.message;
        } else if (errorJson.error) {
          errorMessage = errorJson.error;
        }
      } catch (e) {
        // Keep original error message if JSON parsing fails
      }

      // Create appropriate error type based on status code
      let errorType: 'network' | 'authentication' | 'validation' | 'server' | 'unknown' = 'server';

      if (response.status === 401) {
        errorType = 'authentication';
      } else if (response.status === 403) {
        errorType = 'authentication';
      } else if (response.status === 400) {
        errorType = 'validation';
      } else if (response.status >= 500) {
        errorType = 'server';
      }

      throw new AppError(errorMessage, errorType, response.status, errorDetails);
    }

    return response.json();
  }

  // === AUTHENTICATION ===

  async acceptInvitation(inviteToken: string, password: string): Promise<AuthResponse> {
    this.initialize();

    // Use URLSearchParams instead of FormData for better reverse proxy compatibility
    const formData = new URLSearchParams();
    formData.append('invite_token', inviteToken);
    formData.append('password', password);

    const response = await fetch(`${this.oauthBaseUrl}/employees/accept-invitation`, {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    const result = await this.handleResponse<AuthResponse>(response);

    // Store token
    this.token = result.access_token || result.token || null;
    if (typeof window !== 'undefined' && this.token) {
      localStorage.setItem('auth_token', this.token);
    }

    return result;
  }

  async login(email: string, password: string, workspaceSlug: string): Promise<AuthResponse> {
    this.initialize();

    // Use auth service for new architecture
    const serviceUrl = this.authBaseUrl;
    const serviceName = 'auth-service';

    // Enhanced logging for debugging
    console.log('🔐 Attempting login:', {
      email,
      workspaceSlug,
      architecture: 'NEW',
      serviceUrl,
      serviceName,
      timestamp: new Date().toISOString()
    });

    if (!serviceUrl) {
      throw new AppError(`${serviceName} URL not configured`, 'server');
    }

    const loginUrl = `${serviceUrl}/auth/login`;
    console.log('📡 Login URL:', loginUrl);

    // Primary approach: URLSearchParams (application/x-www-form-urlencoded)
    // This is the most reliable approach for reverse proxy environments
    try {
      console.log('🌐 Using URLSearchParams approach (recommended)...');

      const formData = new URLSearchParams();
      formData.append('email', email);
      formData.append('password', password);
      formData.append('workspace_slug', workspaceSlug);

      const response = await fetch(loginUrl, {
        method: 'POST',
        body: formData,
        signal: AbortSignal.timeout(30000), // 30 second timeout
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('📡 URLSearchParams response:', {
        status: response.status,
        statusText: response.statusText,
        timestamp: new Date().toISOString()
      });

      // If successful, process the response
      if (response.ok || response.status === 401 || response.status === 422) {
        const result = await this.handleResponse<AuthResponse>(response);

        // Store token if login was successful
        if (response.ok) {
          this.token = result.access_token || result.token || null;
          if (typeof window !== 'undefined' && this.token) {
            localStorage.setItem('auth_token', this.token);
          }

          console.log('✅ Login successful with URLSearchParams:', {
            hasToken: !!this.token,
            employee: result.employee?.email,
            tokenLength: this.token?.length,
            fullResult: result,
            employeeType: typeof result.employee,
            employeeKeys: result.employee ? Object.keys(result.employee) : null
          });
        }

        return result;
      }

      // If URLSearchParams fails with 500 error, try FormData fallback
      if (response.status === 500) {
        console.log('⚠️  URLSearchParams returned 500, trying FormData fallback...');
        return await this.loginWithFormDataFallback(email, password, workspaceSlug, loginUrl);
      }

      // For other errors, handle normally
      const result = await this.handleResponse<AuthResponse>(response);
      return result;

    } catch (error) {
      console.log('❌ URLSearchParams approach failed, trying FormData fallback...');
      console.log('Error details:', error);

      // Fallback to FormData approach
      try {
        return await this.loginWithFormDataFallback(email, password, workspaceSlug, loginUrl);
      } catch (fallbackError) {
        // If both approaches fail, throw the original error with enhanced details
        const errorDetails = {
          error: error instanceof Error ? error.message : 'Unknown error',
          type: error instanceof Error ? error.constructor.name : 'Unknown',
          oauthBaseUrl: this.oauthBaseUrl,
          timestamp: new Date().toISOString(),
          fallbackError: fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error'
        };

        console.error('❌ Login failed:', errorDetails);

        // Check if this is a network connectivity issue
        if (error instanceof TypeError && error.message.includes('fetch')) {
          console.error('🚨 Network connectivity issue detected:', {
            message: error.message,
            possibleCauses: [
              'OAuth service is not running',
              'CORS configuration issue',
              'Network connectivity problem',
              'Incorrect OAuth service URL'
            ]
          });

          // Throw a more user-friendly error
          throw new Error('Unable to connect to the server. Please check your internet connection and try again.');
        }

        // Re-throw the error for the UI to handle
        throw error;
      }
    }
  }

  /**
   * Fallback login method using FormData (multipart/form-data)
   * This is used when URLSearchParams fails, primarily for localhost development
   */
  private async loginWithFormDataFallback(
    email: string,
    password: string,
    workspaceSlug: string,
    loginUrl: string
  ): Promise<AuthResponse> {
    console.log('🔄 Attempting FormData fallback approach...');

    const formData = new FormData();
    formData.append('email', email);
    formData.append('password', password);
    formData.append('workspace_slug', workspaceSlug);

    const response = await fetch(loginUrl, {
      method: 'POST',
      body: formData,
      signal: AbortSignal.timeout(30000), // 30 second timeout
      // Don't set Content-Type for FormData - let browser set it with boundary
    });

    console.log('📡 FormData fallback response:', {
      status: response.status,
      statusText: response.statusText,
      timestamp: new Date().toISOString()
    });

    const result = await this.handleResponse<AuthResponse>(response);

    // Store token if login was successful
    if (response.ok) {
      this.token = result.access_token || result.token || null;
      if (typeof window !== 'undefined' && this.token) {
        localStorage.setItem('auth_token', this.token);
      }

      console.log('✅ Login successful with FormData fallback:', {
        hasToken: !!this.token,
        employee: result.employee?.email,
        tokenLength: this.token?.length
      });
    }

    return result;
  }

  async getCurrentUser(): Promise<Employee> {
    this.initialize();

    console.log('🔍 getCurrentUser Debug:', {
      oauthBaseUrl: this.oauthBaseUrl,
      hasToken: !!this.token,
      tokenLength: this.token?.length,
      url: `${this.oauthBaseUrl}/auth/me`
    });

    const response = await fetch(`${this.oauthBaseUrl}/auth/me`, {
      headers: this.getAuthHeaders(),
    });

    console.log('📡 getCurrentUser Response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    // Get raw response text for debugging
    const responseText = await response.text();
    console.log('📄 Raw Response Text:', responseText);

    // Parse the response
    let result;
    try {
      result = JSON.parse(responseText);
      console.log('📊 Parsed Response:', result);
    } catch (parseError) {
      console.error('❌ Failed to parse response:', parseError);
      throw new Error(`Invalid JSON response: ${responseText}`);
    }

    // Check if response is in expected format
    if (!result.employee) {
      console.error('❌ Response missing employee property:', result);
      throw new Error('Invalid response format: missing employee data');
    }

    console.log('👤 getCurrentUser Result:', {
      hasEmployee: !!result.employee,
      email: result.employee?.email,
      workspace: result.employee?.workspace?.slug,
      fullEmployee: result.employee
    });

    return result.employee;
  }

  async logout(): Promise<void> {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  // === OAUTH MANAGEMENT ===

  async getOAuthStatus(): Promise<OAuthStatus> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/google/status';

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<OAuthStatus>(response);
  }

  async initiateGoogleOAuth(services?: string[], redirectUrl?: string): Promise<{ authorization_url: string; auth_url?: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/google/connect';

    console.log('🔗 Initiating Google OAuth:', {
      architecture: 'NEW',
      serviceUrl,
      endpoint,
      services: services || ['gmail', 'drive', 'calendar']
    });

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        services: services || ['gmail', 'drive', 'calendar'],
        redirect_url: redirectUrl || `${window.location.origin}/app/settings`
      }),
    });

    return this.handleResponse<{ authorization_url: string; auth_url?: string }>(response);
  }

  async disconnectGoogleOAuth(): Promise<{ message: string }> {
    const response = await fetch(`${this.oauthBaseUrl}/oauth/google/disconnect`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  // === MCP TOOLS ===

  async getAvailableTools(): Promise<MCPTool[]> {
    this.initialize();

    console.log('🔄 API: Getting available tools...', {
      mcpBaseUrl: this.mcpBaseUrl,
      hasToken: !!this.token,
      tokenLength: this.token?.length
    });

    const response = await fetch(`${this.mcpBaseUrl}/api/tools`, {
      headers: this.getAuthHeaders(),
    });

    console.log('📡 API: Available tools response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url
    });

    const result = await this.handleResponse<{ tools: MCPTool[] }>(response);

    console.log('📊 API: Available tools result:', {
      toolsCount: result.tools?.length || 0
    });

    return result.tools || [];
  }

  // === COMPOSIO INTEGRATION ===

  async createComposioEntity(): Promise<{ success: boolean; entity_id: string; message: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/composio/entities';

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ success: boolean; entity_id: string; message: string }>(response);
  }

  // === NEW MULTI-INTEGRATION ENDPOINTS ===

  async getAvailableIntegrations(): Promise<{ success: boolean; categories: Record<string, any[]>; total_integrations: number }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = '/api/uru/integrations/available';

    console.log('🔄 API: Getting available integrations...', {
      serviceUrl,
      endpoint,
      fullUrl: `${serviceUrl}${endpoint}`,
      hasToken: !!this.token
    });

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });

    console.log('📡 API: Available integrations response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url
    });

    const result = await this.handleResponse<{ success: boolean; categories: Record<string, any[]>; total_integrations: number }>(response);

    console.log('📊 API: Available integrations result:', {
      success: result.success,
      totalIntegrations: result.total_integrations,
      categoriesCount: Object.keys(result.categories || {}).length
    });

    return result;
  }

  async getUserIntegrationConnections(): Promise<{ success: boolean; connections: any[]; total_connections: number }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = '/api/uru/integrations/connections';

    console.log('🔄 API: Getting user integration connections...', {
      serviceUrl,
      endpoint,
      fullUrl: `${serviceUrl}${endpoint}`,
      hasToken: !!this.token
    });

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });

    console.log('📡 API: User connections response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url
    });

    const result = await this.handleResponse<{ success: boolean; connections: any[]; total_connections: number }>(response);

    console.log('📊 API: User connections result:', {
      success: result.success,
      connectionsCount: result.total_connections
    });

    return result;
  }

  async connectIntegration(integrationId: string, options: { redirect_url?: string; metadata?: any } = {}): Promise<{ success: boolean; authorization_url?: string; already_connected?: boolean; message: string }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = `/api/uru/integrations/connect/${integrationId}`;

    console.log('🔗 API: Connecting integration...', {
      integrationId,
      serviceUrl,
      endpoint,
      options
    });

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(options),
    });

    console.log('📡 API: Connect integration response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url
    });

    const result = await this.handleResponse<{ success: boolean; authorization_url?: string; already_connected?: boolean; message: string }>(response);

    console.log('📊 API: Connect integration result:', {
      success: result.success,
      hasAuthUrl: !!result.authorization_url,
      alreadyConnected: result.already_connected
    });

    return result;
  }

  async disconnectIntegration(integrationId: string): Promise<{ success: boolean; integration_id: string; message: string }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = `/api/uru/integrations/disconnect/${integrationId}`;

    console.log('🔌 API: Disconnecting integration...', {
      integrationId,
      serviceUrl,
      endpoint
    });

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    console.log('📡 API: Disconnect integration response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url
    });

    const result = await this.handleResponse<{ success: boolean; integration_id: string; message: string }>(response);

    console.log('📊 API: Disconnect integration result:', {
      success: result.success,
      integrationId: result.integration_id
    });

    return result;
  }

  async connectComposioApp(appName: string, options: { redirect_url?: string; metadata?: any } = {}): Promise<{ success: boolean; authorization_url: string; connection_id: string; message: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = `/integrations/composio/connect/${appName}`;

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(options),
    });

    return this.handleResponse<{ success: boolean; authorization_url: string; connection_id: string; message: string }>(response);
  }

  async getComposioConnections(): Promise<{ success: boolean; connections: any[]; total: number }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/composio/connections';

    console.log('🔄 API: Getting Composio connections...', {
      architecture: 'NEW',
      serviceUrl,
      endpoint,
      fullUrl: `${serviceUrl}${endpoint}`,
      hasToken: !!this.token,
      tokenLength: this.token?.length
    });

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });

    console.log('📡 API: Composio connections response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url
    });

    const result = await this.handleResponse<{ success: boolean; connections: any[]; total: number }>(response);

    console.log('📊 API: Composio connections result:', {
      success: result.success,
      connectionsCount: result.connections?.length || 0
    });

    return result;
  }

  async disconnectComposioApp(appName: string): Promise<{ success: boolean; app_name: string; message: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = `/integrations/composio/connections/${appName}`;

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ success: boolean; app_name: string; message: string }>(response);
  }

  async executeComposioTool(toolName: string, parameters: any = {}): Promise<{ success: boolean; tool_name: string; result: any; execution_time?: number; message: string }> {
    this.initialize();
    const response = await fetch(`${this.mcpBaseUrl}/api/tools/execute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        tool_name: toolName,
        parameters: parameters
      }),
    });

    return this.handleResponse<{ success: boolean; tool_name: string; result: any; execution_time?: number; message: string }>(response);
  }

  async executeTool(toolName: string, parameters: any = {}): Promise<MCPToolResult> {
    const response = await fetch(`${this.mcpBaseUrl}/api/tools/execute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        tool_name: toolName,
        parameters: parameters
      }),
    });

    return this.handleResponse<MCPToolResult>(response);
  }

  // === INTELLIGENCE QUERIES ===

  async queryIntelligence(query: string): Promise<any> {
    // This would use the MCP tools to query company data
    // For now, we'll simulate with the transcript query tool
    return this.executeTool('Transcript_Log_Query', { input: query });
  }

  async getClientInsights(): Promise<any> {
    // Query client data for insights
    return this.executeTool('Client_Table_Query', { input: 'recent client interactions and trends' });
  }

  async getTranscriptAnalysis(query: string): Promise<any> {
    return this.executeTool('Transcript_Log_Query', { input: query });
  }

  // === HEALTH CHECKS ===

  async checkOAuthHealth(): Promise<{ status: string; database: string }> {
    this.initialize();
    const response = await fetch(`${this.oauthBaseUrl}/health`);
    return this.handleResponse<{ status: string; database: string }>(response);
  }

  // === MCP CONFIGURATION ===

  async generateMCPConfigWithUser(userEmail: string, workspaceSlug: string): Promise<{ success: boolean; config: any; token: string; expires_at: string; message: string }> {
    this.initialize();

    console.log('🔧 Generating MCP Config with provided user data...');
    console.log('👤 User data:', { email: userEmail, workspace: workspaceSlug });

    // Generate MCP configuration using auth service
    const requestBody = new URLSearchParams({
      employee_email: userEmail,
      workspace_slug: workspaceSlug,
      days_valid: '90'
    });

    console.log('📤 Request details:', {
      url: `${this.authBaseUrl}/admin/claude-desktop-token`,
      body: requestBody.toString()
    });

    const response = await fetch(`${this.authBaseUrl}/admin/claude-desktop-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: requestBody
    });

    console.log('📥 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ MCP Config Error Response:', errorText);
      throw new Error(`Failed to generate MCP configuration (${response.status}): ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Auth service response:', result);

    // Create Claude Desktop configuration
    const config = {
      mcpServers: {
        "uru-platform": {
          command: "node",
          args: ["./uru-claude-desktop-server.js"],
          env: {
            MCP_PROXY_URL: this.mcpBaseUrl,
            EMPLOYEE_TOKEN: result.claude_desktop_token,
            DEBUG_MODE: "false"
          }
        }
      }
    };

    return {
      success: true,
      config: config,
      token: result.claude_desktop_token,
      expires_at: result.expires_at,
      message: result.message || 'Configuration generated successfully'
    };
  }

  async generateMCPConfig(): Promise<{ success: boolean; config: any; token: string; expires_at: string; message: string }> {
    this.initialize();

    console.log('🔧 Generating MCP Config...');
    console.log('🔑 API Service Debug:', {
      hasToken: !!this.token,
      tokenLength: this.token?.length,
      tokenPreview: this.token ? `${this.token.substring(0, 20)}...` : 'null',
      authBaseUrl: this.authBaseUrl
    });

    // Get current user info with enhanced error handling
    let user;
    try {
      console.log('🔍 About to call getCurrentUser...');
      user = await this.getCurrentUser();
      console.log('✅ getCurrentUser returned:', {
        userExists: !!user,
        userType: typeof user,
        hasEmail: !!user?.email,
        hasWorkspace: !!user?.workspace,
        email: user?.email,
        workspaceSlug: user?.workspace?.slug,
        fullUserObject: user
      });
    } catch (error) {
      console.error('❌ getCurrentUser failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate user data
    if (!user) {
      console.error('❌ No user data returned');
      throw new Error('No user data returned from authentication service');
    }

    console.log('🔍 User validation - checking email...');
    if (!user.email) {
      console.error('❌ User object missing email:', {
        user: user,
        userKeys: Object.keys(user || {}),
        userEmail: user?.email,
        userEmailType: typeof user?.email
      });
      throw new Error('User email not found - authentication may be incomplete');
    }

    console.log('🔍 User validation - checking workspace...');
    if (!user.workspace) {
      console.error('❌ User object missing workspace:', {
        user: user,
        userKeys: Object.keys(user || {}),
        userWorkspace: user?.workspace,
        userWorkspaceType: typeof user?.workspace
      });
      throw new Error('User workspace not found - please ensure you are logged into a workspace');
    }

    // Generate MCP configuration using auth service
    const requestBody = new URLSearchParams({
      employee_email: user.email,
      workspace_slug: user.workspace?.slug || 'default',
      days_valid: '90'
    });

    console.log('📤 Request details:', {
      url: `${this.authBaseUrl}/admin/claude-desktop-token`,
      body: requestBody.toString()
    });

    const response = await fetch(`${this.authBaseUrl}/admin/claude-desktop-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: requestBody
    });

    console.log('📥 Response status:', response.status);

    const result = await this.handleResponse<{
      claude_desktop_token: string;
      expires_at: string;
      days_valid: number;
      message: string
    }>(response);

    // Create Claude Desktop configuration
    const config = {
      mcpServers: {
        "uru-platform": {
          command: "node",
          args: ["./uru-claude-desktop-server.js"],
          env: {
            MCP_PROXY_URL: this.mcpBaseUrl,
            EMPLOYEE_TOKEN: result.claude_desktop_token,
            DEBUG_MODE: "false"
          }
        }
      }
    };

    return {
      success: true,
      config: config,
      token: result.claude_desktop_token,
      expires_at: result.expires_at,
      message: result.message
    };
  }

  async checkMCPHealth(): Promise<{ status: string; n8n_connection: any }> {
    this.initialize();
    const response = await fetch(`${this.mcpBaseUrl}/health`);
    return this.handleResponse<{ status: string; n8n_connection: any }>(response);
  }

  // === DEBUGGING METHODS ===

  async testComposioIntegration(): Promise<{
    auth: boolean;
    oauthService: boolean;
    mcpProxy: boolean;
    composioConnections: boolean;
    availableTools: boolean;
    errors: string[];
  }> {
    const result = {
      auth: false,
      oauthService: false,
      mcpProxy: false,
      composioConnections: false,
      availableTools: false,
      errors: [] as string[]
    };

    console.log('🧪 Testing Composio Integration...');

    // Test 1: Authentication
    try {
      result.auth = this.isAuthenticated();
      console.log(`✅ Auth test: ${result.auth ? 'PASS' : 'FAIL'}`);
      if (!result.auth) {
        result.errors.push('No authentication token found');
      }
    } catch (error) {
      result.errors.push(`Auth test failed: ${error}`);
      console.log(`❌ Auth test: FAIL - ${error}`);
    }

    // Test 2: OAuth Service Health
    try {
      const oauthHealth = await this.checkOAuthHealth();
      result.oauthService = oauthHealth.status === 'healthy';
      console.log(`✅ OAuth service test: ${result.oauthService ? 'PASS' : 'FAIL'}`);
      if (!result.oauthService) {
        result.errors.push(`OAuth service unhealthy: ${oauthHealth.status}`);
      }
    } catch (error) {
      result.errors.push(`OAuth service test failed: ${error}`);
      console.log(`❌ OAuth service test: FAIL - ${error}`);
    }

    // Test 3: MCP Proxy Health
    try {
      const mcpHealth = await this.checkMCPHealth();
      result.mcpProxy = mcpHealth.status === 'Smart MCP Proxy running';
      console.log(`✅ MCP proxy test: ${result.mcpProxy ? 'PASS' : 'FAIL'}`);
      if (!result.mcpProxy) {
        result.errors.push(`MCP proxy unhealthy: ${mcpHealth.status}`);
      }
    } catch (error) {
      result.errors.push(`MCP proxy test failed: ${error}`);
      console.log(`❌ MCP proxy test: FAIL - ${error}`);
    }

    // Test 4: Composio Connections
    if (result.auth && result.oauthService) {
      try {
        const connections = await this.getComposioConnections();
        result.composioConnections = connections.success;
        console.log(`✅ Composio connections test: ${result.composioConnections ? 'PASS' : 'FAIL'}`);
        if (!result.composioConnections) {
          result.errors.push('Failed to load Composio connections');
        }
      } catch (error) {
        result.errors.push(`Composio connections test failed: ${error}`);
        console.log(`❌ Composio connections test: FAIL - ${error}`);
      }
    } else {
      result.errors.push('Skipping Composio connections test - prerequisites failed');
    }

    // Test 5: Available Tools
    if (result.auth && result.mcpProxy) {
      try {
        const tools = await this.getAvailableTools();
        result.availableTools = Array.isArray(tools) && tools.length > 0;
        console.log(`✅ Available tools test: ${result.availableTools ? 'PASS' : 'FAIL'} (${tools.length} tools)`);
        if (!result.availableTools) {
          result.errors.push('No available tools found');
        }
      } catch (error) {
        result.errors.push(`Available tools test failed: ${error}`);
        console.log(`❌ Available tools test: FAIL - ${error}`);
      }
    } else {
      result.errors.push('Skipping available tools test - prerequisites failed');
    }

    console.log('🧪 Composio Integration Test Results:', result);
    return result;
  }

  // === INVITATION MANAGEMENT ===

  async validateInvitation(inviteToken: string): Promise<any> {
    const response = await fetch(`${this.oauthBaseUrl}/employees/validate-invitation/${inviteToken}`);
    return this.handleResponse<any>(response);
  }

  // === WORKSPACE MANAGEMENT ===

  async getWorkspaces(): Promise<any[]> {
    const response = await fetch(`${this.oauthBaseUrl}/workspaces`);
    const result = await this.handleResponse<{ workspaces: any[] }>(response);
    return result.workspaces;
  }

  async createWorkspace(name: string, slug: string): Promise<any> {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('slug', slug);

    const response = await fetch(`${this.oauthBaseUrl}/workspaces`, {
      method: 'POST',
      body: formData,
    });

    return this.handleResponse<any>(response);
  }

  async createInvitation(workspaceId: string, email: string, name: string, role: string = 'member'): Promise<any> {
    const formData = new FormData();
    formData.append('workspace_id', workspaceId);
    formData.append('email', email);
    formData.append('name', name);
    formData.append('role', role);

    const response = await fetch(`${this.oauthBaseUrl}/employees/invite`, {
      method: 'POST',
      body: formData,
    });

    return this.handleResponse<any>(response);
  }

  // === UTILITY METHODS ===

  isAuthenticated(): boolean {
    return !!this.token;
  }

  setToken(token: string): void {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  getToken(): string | null {
    return this.token;
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
