// frontend/utils/api.ts
// API service for connecting to OAuth service and MCP proxy

import { AppError, handleError } from './errorHandler';

interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface Employee {
  id: string;
  email: string;
  name: string;
  role: string;
  workspace: {
    id: string;
    name: string;
    slug: string;
  };
}

interface AuthResponse {
  employee: Employee;
  access_token: string;
  token?: string; // For backward compatibility
  expires_at: string;
}

interface OAuthStatus {
  connected: boolean;
  expired?: boolean;
  scopes?: string[];
  expires_at?: string;
  user_info?: {
    email?: string;
    name?: string;
  };
  needs_refresh?: boolean;
  message?: string;
}

interface MCPTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: any;
  };
}

interface MCPToolResult {
  success: boolean;
  tool_name: string;
  result: any;
  timestamp: string;
}

class ApiService {
  private oauthBaseUrl: string | null = null;
  private authBaseUrl: string | null = null;
  private integrationsBaseUrl: string | null = null;
  private mcpBaseUrl: string | null = null;
  private token: string | null = null;
  private initialized = false;

  private initialize() {
    if (this.initialized) return;

    // Always use new architecture

    // Enhanced environment detection for Elestio deployment
    const isProduction = process.env.NODE_ENV === 'production' ||
                        (typeof window !== 'undefined' && window.location.hostname.includes('uruenterprises.com'));

    // Get environment variables with fallbacks
    const authUrl = process.env.NEXT_PUBLIC_AUTH_URL;
    const integrationsUrl = process.env.NEXT_PUBLIC_INTEGRATIONS_URL;
    const mcpUrl = process.env.NEXT_PUBLIC_MCP_URL;

    if (isProduction) {
      // Production URL configuration - New architecture only
      this.authBaseUrl = authUrl || 'https://auth.uruenterprises.com';
      this.integrationsBaseUrl = integrationsUrl || 'https://integrations.uruenterprises.com';
      this.oauthBaseUrl = this.authBaseUrl; // For backward compatibility
      this.mcpBaseUrl = mcpUrl || 'https://mcp.uruenterprises.com';

    } else {
      // Development defaults to localhost - New architecture only
      this.authBaseUrl = authUrl || 'http://localhost:8003';
      this.integrationsBaseUrl = integrationsUrl || 'http://localhost:8002';
      this.oauthBaseUrl = this.authBaseUrl; // For backward compatibility
      this.mcpBaseUrl = mcpUrl || 'http://localhost:3001';

    }

    // Try to get token from localStorage on client side
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }

    this.initialized = true;
  }

  private getAuthHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    
    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text();

      // Try to parse error as JSON for better error messages
      let errorMessage = `API Error: ${response.status} - ${errorText}`;
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.detail) {
          // Handle both string and object detail formats
          if (typeof errorJson.detail === 'string') {
            errorMessage = errorJson.detail;
          } else if (typeof errorJson.detail === 'object' && errorJson.detail.message) {
            errorMessage = errorJson.detail.message;
          } else if (typeof errorJson.detail === 'object' && errorJson.detail.error) {
            errorMessage = errorJson.detail.error;
          } else {
            errorMessage = JSON.stringify(errorJson.detail);
          }
        } else if (errorJson.message) {
          errorMessage = errorJson.message;
        } else if (errorJson.error) {
          errorMessage = errorJson.error;
        }
      } catch (e) {
        // Keep original error message if JSON parsing fails
      }

      // Create appropriate error type based on status code
      let errorType: 'network' | 'authentication' | 'validation' | 'server' | 'unknown' = 'server';

      if (response.status === 401) {
        errorType = 'authentication';
      } else if (response.status === 403) {
        errorType = 'authentication';
      } else if (response.status === 400) {
        errorType = 'validation';
      } else if (response.status >= 500) {
        errorType = 'server';
      }

      throw new AppError(errorMessage, errorType, response.status, errorDetails);
    }

    return response.json();
  }

  // === AUTHENTICATION ===

  async acceptInvitation(inviteToken: string, password: string): Promise<AuthResponse> {
    this.initialize();

    // Use URLSearchParams instead of FormData for better reverse proxy compatibility
    const formData = new URLSearchParams();
    formData.append('invite_token', inviteToken);
    formData.append('password', password);

    const response = await fetch(`${this.oauthBaseUrl}/employees/accept-invitation`, {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    const result = await this.handleResponse<AuthResponse>(response);

    // Store token
    this.token = result.access_token || result.token || null;
    if (typeof window !== 'undefined' && this.token) {
      localStorage.setItem('auth_token', this.token);
    }

    return result;
  }

  async login(email: string, password: string, workspaceSlug: string): Promise<AuthResponse> {
    this.initialize();

    // Use auth service for new architecture
    const serviceUrl = this.authBaseUrl;
    const serviceName = 'auth-service';

    if (!serviceUrl) {
      throw new AppError(`${serviceName} URL not configured`, 'server');
    }

    const loginUrl = `${serviceUrl}/auth/login`;

    // Primary approach: URLSearchParams (application/x-www-form-urlencoded)
    // This is the most reliable approach for reverse proxy environments
    try {

      const formData = new URLSearchParams();
      formData.append('email', email);
      formData.append('password', password);
      formData.append('workspace_slug', workspaceSlug);

      const response = await fetch(loginUrl, {
        method: 'POST',
        body: formData,
        signal: AbortSignal.timeout(30000), // 30 second timeout
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      // If successful, process the response
      if (response.ok || response.status === 401 || response.status === 422) {
        const result = await this.handleResponse<AuthResponse>(response);

        // Store token if login was successful
        if (response.ok) {
          this.token = result.access_token || result.token || null;
          if (typeof window !== 'undefined' && this.token) {
            localStorage.setItem('auth_token', this.token);
          }}

        return result;
      }

      // If URLSearchParams fails with 500 error, try FormData fallback
      if (response.status === 500) {
        return await this.loginWithFormDataFallback(email, password, workspaceSlug, loginUrl);
      }

      // For other errors, handle normally
      const result = await this.handleResponse<AuthResponse>(response);
      return result;

    } catch (error) {
      // Fallback to FormData approach
      try {
        return await this.loginWithFormDataFallback(email, password, workspaceSlug, loginUrl);
      } catch (fallbackError) {
        // If both approaches fail, throw the original error with enhanced details
        const errorDetails = {
          error: error instanceof Error ? error.message : 'Unknown error',
          type: error instanceof Error ? error.constructor.name : 'Unknown',
          oauthBaseUrl: this.oauthBaseUrl,
          timestamp: new Date().toISOString(),
          fallbackError: fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error'
        };

        // Check if this is a network connectivity issue
        if (error instanceof TypeError && error.message.includes('fetch')) {
          // Throw a more user-friendly error
          throw new Error('Unable to connect to the server. Please check your internet connection and try again.');
        }

        // Re-throw the error for the UI to handle
        throw error;
      }
    }
  }

  /**
   * Fallback login method using FormData (multipart/form-data)
   * This is used when URLSearchParams fails, primarily for localhost development
   */
  private async loginWithFormDataFallback(
    email: string,
    password: string,
    workspaceSlug: string,
    loginUrl: string
  ): Promise<AuthResponse> {
    const formData = new FormData();
    formData.append('email', email);
    formData.append('password', password);
    formData.append('workspace_slug', workspaceSlug);

    const response = await fetch(loginUrl, {
      method: 'POST',
      body: formData,
      signal: AbortSignal.timeout(30000), // 30 second timeout
      // Don't set Content-Type for FormData - let browser set it with boundary
    });const result = await this.handleResponse<AuthResponse>(response);

    // Store token if login was successful
    if (response.ok) {
      this.token = result.access_token || result.token || null;
      if (typeof window !== 'undefined' && this.token) {
        localStorage.setItem('auth_token', this.token);
      }}

    return result;
  }

  async getCurrentUser(): Promise<Employee> {
    this.initialize();

    const response = await fetch(`${this.authBaseUrl}/auth/me`, {
      headers: this.getAuthHeaders(),
    });

    // Get raw response text
    const responseText = await response.text();

    // Parse the response
    let result;
    try {
      result = JSON.parse(responseText);} catch (parseError) {throw new Error(`Invalid JSON response: ${responseText}`);
    }

    // Check if response is in expected format
    if (!result.employee) {throw new Error('Invalid response format: missing employee data');
    }return result.employee;
  }

  async logout(): Promise<void> {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  // === OAUTH MANAGEMENT ===

  async getOAuthStatus(): Promise<OAuthStatus> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/google/status';

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<OAuthStatus>(response);
  }

  async initiateGoogleOAuth(services?: string[], redirectUrl?: string): Promise<{ authorization_url: string; auth_url?: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/google/connect';const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        services: services || ['gmail', 'drive', 'calendar'],
        redirect_url: redirectUrl || `${window.location.origin}/app/settings`
      }),
    });

    return this.handleResponse<{ authorization_url: string; auth_url?: string }>(response);
  }

  async disconnectGoogleOAuth(): Promise<{ message: string }> {
    const response = await fetch(`${this.oauthBaseUrl}/oauth/google/disconnect`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  // === MCP TOOLS ===

  async getAvailableTools(): Promise<MCPTool[]> {
    this.initialize();const response = await fetch(`${this.mcpBaseUrl}/api/tools`, {
      headers: this.getAuthHeaders(),
    });const result = await this.handleResponse<{ tools: MCPTool[] }>(response);return result.tools || [];
  }

  // === COMPOSIO INTEGRATION ===

  async createComposioEntity(): Promise<{ success: boolean; entity_id: string; message: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/composio/entities';

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ success: boolean; entity_id: string; message: string }>(response);
  }

  // === NEW MULTI-INTEGRATION ENDPOINTS ===

  async getAvailableIntegrations(): Promise<{ success: boolean; categories: Record<string, any[]>; total_integrations: number }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = '/api/uru/integrations/available';const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });const result = await this.handleResponse<{ success: boolean; categories: Record<string, any[]>; total_integrations: number }>(response);return result;
  }

  async getUserIntegrationConnections(): Promise<{ success: boolean; connections: any[]; total_connections: number }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = '/api/uru/integrations/connections';const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });const result = await this.handleResponse<{ success: boolean; connections: any[]; total_connections: number }>(response);return result;
  }

  async connectIntegration(integrationId: string, options: { redirect_url?: string; metadata?: any } = {}): Promise<{ success: boolean; authorization_url?: string; already_connected?: boolean; message: string }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = `/api/uru/integrations/connect/${integrationId}`;const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(options),
    });const result = await this.handleResponse<{ success: boolean; authorization_url?: string; already_connected?: boolean; message: string }>(response);return result;
  }

  async disconnectIntegration(integrationId: string): Promise<{ success: boolean; integration_id: string; message: string }> {
    this.initialize();

    // Use composio service for new multi-integration architecture
    const serviceUrl = process.env.NEXT_PUBLIC_COMPOSIO_URL || 'http://localhost:8001';
    const endpoint = `/api/uru/integrations/disconnect/${integrationId}`;const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });const result = await this.handleResponse<{ success: boolean; integration_id: string; message: string }>(response);return result;
  }

  async connectComposioApp(appName: string, options: { redirect_url?: string; metadata?: any } = {}): Promise<{ success: boolean; authorization_url: string; connection_id: string; message: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = `/integrations/composio/connect/${appName}`;

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(options),
    });

    return this.handleResponse<{ success: boolean; authorization_url: string; connection_id: string; message: string }>(response);
  }

  async getComposioConnections(): Promise<{ success: boolean; connections: any[]; total: number }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = '/integrations/composio/connections';const response = await fetch(`${serviceUrl}${endpoint}`, {
      headers: this.getAuthHeaders(),
    });const result = await this.handleResponse<{ success: boolean; connections: any[]; total: number }>(response);return result;
  }

  async disconnectComposioApp(appName: string): Promise<{ success: boolean; app_name: string; message: string }> {
    this.initialize();

    // Use integrations service for new architecture
    const serviceUrl = this.integrationsBaseUrl;
    const endpoint = `/integrations/composio/connections/${appName}`;

    const response = await fetch(`${serviceUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ success: boolean; app_name: string; message: string }>(response);
  }

  async executeComposioTool(toolName: string, parameters: any = {}): Promise<{ success: boolean; tool_name: string; result: any; execution_time?: number; message: string }> {
    this.initialize();
    const response = await fetch(`${this.mcpBaseUrl}/api/tools/execute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        tool_name: toolName,
        parameters: parameters
      }),
    });

    return this.handleResponse<{ success: boolean; tool_name: string; result: any; execution_time?: number; message: string }>(response);
  }

  async executeTool(toolName: string, parameters: any = {}): Promise<MCPToolResult> {
    const response = await fetch(`${this.mcpBaseUrl}/api/tools/execute`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        tool_name: toolName,
        parameters: parameters
      }),
    });

    return this.handleResponse<MCPToolResult>(response);
  }

  // === INTELLIGENCE QUERIES ===

  async queryIntelligence(query: string): Promise<any> {
    // This would use the MCP tools to query company data
    // For now, we'll simulate with the transcript query tool
    return this.executeTool('Transcript_Log_Query', { input: query });
  }

  async getClientInsights(): Promise<any> {
    // Query client data for insights
    return this.executeTool('Client_Table_Query', { input: 'recent client interactions and trends' });
  }

  async getTranscriptAnalysis(query: string): Promise<any> {
    return this.executeTool('Transcript_Log_Query', { input: query });
  }

  // === HEALTH CHECKS ===

  async checkOAuthHealth(): Promise<{ status: string; database: string }> {
    this.initialize();
    const response = await fetch(`${this.oauthBaseUrl}/health`);
    return this.handleResponse<{ status: string; database: string }>(response);
  }

  // === MCP CONFIGURATION ===

  async generateMCPConfigWithUser(userEmail: string, workspaceSlug: string): Promise<{ success: boolean; config: any; token: string; expires_at: string; message: string }> {
    this.initialize();// Generate MCP configuration using auth service
    const requestBody = new URLSearchParams({
      employee_email: userEmail,
      workspace_slug: workspaceSlug,
      days_valid: '90'
    });const response = await fetch(`${this.authBaseUrl}/admin/claude-desktop-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: requestBody
    });if (!response.ok) {
      const errorText = await response.text();throw new Error(`Failed to generate MCP configuration (${response.status}): ${errorText}`);
    }

    const result = await response.json();// Create Claude Desktop configuration
    const config = {
      mcpServers: {
        "uru-platform": {
          command: "node",
          args: ["./uru-claude-desktop-server.js"],
          env: {
            MCP_PROXY_URL: this.mcpBaseUrl,
            EMPLOYEE_TOKEN: result.claude_desktop_token,
            DEBUG_MODE: "false"
          }
        }
      }
    };

    return {
      success: true,
      config: config,
      token: result.claude_desktop_token,
      expires_at: result.expires_at,
      message: result.message || 'Configuration generated successfully'
    };
  }

  async generateMCPConfig(): Promise<{ success: boolean; config: any; token: string; expires_at: string; message: string }> {
    this.initialize();// Get current user info with enhanced error handling
    let user;
    try {user = await this.getCurrentUser();} catch (error) {throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate user data
    if (!user) {throw new Error('No user data returned from authentication service');
    }if (!user.email) {throw new Error('User email not found - authentication may be incomplete');
    }if (!user.workspace) {throw new Error('User workspace not found - please ensure you are logged into a workspace');
    }

    // Generate MCP configuration using auth service
    const requestBody = new URLSearchParams({
      employee_email: user.email,
      workspace_slug: user.workspace?.slug || 'default',
      days_valid: '90'
    });const response = await fetch(`${this.authBaseUrl}/admin/claude-desktop-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: requestBody
    });const result = await this.handleResponse<{
      claude_desktop_token: string;
      expires_at: string;
      days_valid: number;
      message: string
    }>(response);

    // Create Claude Desktop configuration
    const config = {
      mcpServers: {
        "uru-platform": {
          command: "node",
          args: ["./uru-claude-desktop-server.js"],
          env: {
            MCP_PROXY_URL: this.mcpBaseUrl,
            EMPLOYEE_TOKEN: result.claude_desktop_token,
            DEBUG_MODE: "false"
          }
        }
      }
    };

    return {
      success: true,
      config: config,
      token: result.claude_desktop_token,
      expires_at: result.expires_at,
      message: result.message
    };
  }

  async checkMCPHealth(): Promise<{ status: string; n8n_connection: any }> {
    this.initialize();
    const response = await fetch(`${this.mcpBaseUrl}/health`);
    return this.handleResponse<{ status: string; n8n_connection: any }>(response);
  }



  // === INVITATION MANAGEMENT ===

  async validateInvitation(inviteToken: string): Promise<any> {
    const response = await fetch(`${this.oauthBaseUrl}/employees/validate-invitation/${inviteToken}`);
    return this.handleResponse<any>(response);
  }

  // === WORKSPACE MANAGEMENT ===

  async getWorkspaces(): Promise<any[]> {
    const response = await fetch(`${this.oauthBaseUrl}/workspaces`);
    const result = await this.handleResponse<{ workspaces: any[] }>(response);
    return result.workspaces;
  }

  async createWorkspace(name: string, slug: string): Promise<any> {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('slug', slug);

    const response = await fetch(`${this.oauthBaseUrl}/workspaces`, {
      method: 'POST',
      body: formData,
    });

    return this.handleResponse<any>(response);
  }

  async createInvitation(workspaceId: string, email: string, name: string, role: string = 'member'): Promise<any> {
    const formData = new FormData();
    formData.append('workspace_id', workspaceId);
    formData.append('email', email);
    formData.append('name', name);
    formData.append('role', role);

    const response = await fetch(`${this.oauthBaseUrl}/employees/invite`, {
      method: 'POST',
      body: formData,
    });

    return this.handleResponse<any>(response);
  }

  // === UTILITY METHODS ===

  isAuthenticated(): boolean {
    return !!this.token;
  }

  setToken(token: string): void {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  getToken(): string | null {
    return this.token;
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
