# auth-service/main.py
# Uru Platform Authentication Service - User Identity Management

from fastapi import FastAPI, HTTPException, Depends, status, Form, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from supabase import create_client, Client
from typing import Optional, List
from datetime import datetime, timedelta
import asyncio
import logging
import secrets
import jwt
import os
import sys
from dotenv import load_dotenv
from contextlib import asynccontextmanager

# Add shared directory to path for environment utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))
try:
    from environment import env_config
    print(f"Environment detected: {env_config.environment.value}")
except ImportError:
    print("Environment utilities not found, using fallback configuration")
    env_config = None

# Import our custom modules
from auth import EmployeeAuth

logging.basicConfig(level=logging.INFO)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - start/stop background tasks"""
    # Startup
    print("Starting Uru Authentication Service...")
    
    yield

    # Shutdown
    print("Shutting down Authentication Service...")

# Create FastAPI app
app = FastAPI(
    title="Uru Authentication Service",
    version="2025.1",
    description="User authentication and identity management for Uru Platform",
    lifespan=lifespan
)

# CORS configuration
if env_config:
    allowed_origins = env_config.get_cors_origins()
else:
    allowed_origins = [
        "http://localhost:3000",
        "https://app.uruenterprises.com",
        "https://auth.uruenterprises.com"
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Global variables for auth systems
supabase = None
employee_auth = None
auth_initialized = False

# Initialize Supabase
def initialize_supabase():
    """Initialize Supabase client with retry logic"""
    global supabase
    
    try:
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            print("⚠️ Supabase credentials not found in environment")
            return None
            
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Supabase client initialized successfully")
        return supabase
        
    except Exception as e:
        print(f"❌ Supabase initialization failed: {e}")
        return None

# Initialize auth systems
def initialize_auth_systems():
    """Initialize authentication systems with retry logic"""
    global employee_auth

    if supabase:
        try:
            employee_auth = EmployeeAuth(supabase)
            print("✅ Authentication systems initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Auth system initialization failed: {e}")
            employee_auth = None
            return False
    else:
        print("⚠️ Auth systems disabled due to Supabase connection failure")
        employee_auth = None
        return False

# Initialize systems on startup
supabase = initialize_supabase()
auth_initialized = initialize_auth_systems()

# Security
security = HTTPBearer(auto_error=False)

async def get_current_employee(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated employee"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    try:
        employee = await employee_auth.get_current_employee(credentials.credentials)
        if not employee:
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication token"
            )
        return employee
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail=f"Authentication failed: {str(e)}"
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    # Check database connection
    db_status = "connected" if supabase else "disconnected"
    
    # Check authentication service status
    auth_status = "enabled" if employee_auth else "disabled"
    
    return {
        "status": "healthy" if (supabase and employee_auth) else "degraded",
        "service": "auth-service",
        "version": "2025.1",
        "database": db_status,
        "authentication": auth_status,
        "timestamp": datetime.utcnow().isoformat()
    }

# Authentication endpoints
@app.post("/auth/login")
async def login_employee(
    email: str = Form(...),
    password: str = Form(...),
    workspace_slug: str = Form(...)
):
    """Employee login endpoint"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    try:
        result = await employee_auth.login(email, password, workspace_slug)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Login failed: {str(e)}"
        )

@app.get("/auth/me")
async def get_current_user(employee = Depends(get_current_employee)):
    """Get current authenticated user information"""
    # The employee data from get_current_employee already includes workspace info
    # Format it to match frontend expectations
    workspace_data = employee.get("workspaces", {})

    return {
        "employee": {
            "id": employee["id"],
            "email": employee["email"],
            "name": employee.get("name") or f"{employee.get('first_name', '')} {employee.get('last_name', '')}".strip(),
            "role": employee.get("role", "employee"),
            "workspace": {
                "id": workspace_data.get("id") if workspace_data else employee.get("workspace_id"),
                "name": workspace_data.get("name", "Default Workspace") if workspace_data else "Default Workspace",
                "slug": workspace_data.get("slug", "default") if workspace_data else "default"
            }
        }
    }

@app.post("/auth/logout")
async def logout_employee(employee = Depends(get_current_employee)):
    """Employee logout endpoint"""
    # For JWT-based auth, logout is typically handled client-side
    # But we can invalidate tokens if needed in the future
    return {"message": "Logged out successfully"}

@app.post("/auth/refresh")
async def refresh_token(employee = Depends(get_current_employee)):
    """Refresh authentication token"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    try:
        # Generate new token for the employee
        new_token = await employee_auth.generate_token(employee["id"])
        return {"access_token": new_token, "token_type": "bearer"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Token refresh failed: {str(e)}"
        )

# Employee management endpoints
@app.post("/employees/invite")
async def invite_employee(
    email: str = Form(...),
    first_name: str = Form(...),
    last_name: str = Form(...),
    role: str = Form("employee"),
    employee = Depends(get_current_employee)
):
    """Invite new employee to workspace"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    # Check if current employee has permission to invite
    if employee.get("role") not in ["admin", "owner"]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to invite employees"
        )
    
    try:
        result = await employee_auth.invite_employee(
            email, first_name, last_name, role, employee["workspace_id"]
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Employee invitation failed: {str(e)}"
        )

@app.post("/employees/accept-invitation")
async def accept_invitation(
    token: str = Form(...),
    password: str = Form(...)
):
    """Accept employee invitation and set password"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    
    try:
        result = await employee_auth.accept_invitation(token, password)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Invitation acceptance failed: {str(e)}"
        )

# Admin endpoints
@app.post("/admin/retry-auth-init")
async def retry_auth_initialization():
    """Retry authentication system initialization (admin endpoint)"""
    global supabase, employee_auth, auth_initialized

    # Try to reinitialize Supabase
    supabase = initialize_supabase()

    # Try to reinitialize auth systems
    auth_initialized = initialize_auth_systems()

    return {
        "supabase_connected": supabase is not None,
        "auth_systems_initialized": auth_initialized,
        "employee_auth_available": employee_auth is not None,
        "message": "Reinitialization attempt completed"
    }

@app.post("/dev/test-invitation")
async def create_test_invitation():
    """Create a test invitation for development (dev endpoint)"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Create or get test workspace
        workspace_result = supabase.table('workspaces').select("*").eq('slug', 'test-workspace').execute()

        if not workspace_result.data:
            # Create test workspace
            workspace_data = {
                "name": "Test Workspace",
                "slug": "test-workspace",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            workspace_result = supabase.table('workspaces').insert(workspace_data).execute()

        workspace = workspace_result.data[0]

        # Create test invitation
        result = await employee_auth.create_employee_invitation(
            workspace["id"],
            "<EMAIL>",
            "Dev User",
            "admin"
        )

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Test invitation creation failed: {str(e)}"
        )

@app.post("/dev/test-user")
async def create_test_user():
    """Create a test user with known credentials for development (dev endpoint)"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Create or get test workspace
        workspace_result = supabase.table('workspaces').select("*").eq('slug', 'test-workspace').execute()

        if not workspace_result.data:
            # Create test workspace
            workspace_data = {
                "name": "Test Workspace",
                "slug": "test-workspace",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            workspace_result = supabase.table('workspaces').insert(workspace_data).execute()

        workspace = workspace_result.data[0]

        # Check if test user already exists
        existing_user = supabase.table('employees').select("*").eq('email', '<EMAIL>').execute()

        if existing_user.data:
            return {
                "message": "Test user already exists",
                "email": "<EMAIL>",
                "workspace_slug": "test-workspace",
                "password": "testpassword123"
            }

        # Create test user directly with password
        password_hash = employee_auth._hash_password("testpassword123")

        test_user_data = {
            "workspace_id": workspace["id"],
            "email": "<EMAIL>",
            "name": "Test User",
            "role": "admin",
            "status": "active",
            "password_hash": password_hash,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        user_result = supabase.table('employees').insert(test_user_data).execute()

        if user_result.data:
            return {
                "message": "Test user created successfully",
                "email": "<EMAIL>",
                "workspace_slug": "test-workspace",
                "password": "testpassword123",
                "user_id": user_result.data[0]["id"]
            }
        else:
            raise Exception("Failed to create test user")

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Test user creation failed: {str(e)}"
        )

@app.post("/admin/claude-desktop-token")
async def generate_claude_desktop_token(
    employee_email: str = Form(...),
    workspace_slug: str = Form(...),
    days_valid: int = Form(90)
):
    """Generate Claude Desktop token for employee (admin endpoint)"""
    if not employee_auth:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )

    try:
        # Find employee by email and workspace
        employee_result = supabase.table('employees').select(
            "id, email, name, role, workspace_id, workspaces(slug)"
        ).eq('email', employee_email).execute()

        if not employee_result.data:
            raise HTTPException(
                status_code=404,
                detail=f"Employee not found: {employee_email}"
            )

        employee = employee_result.data[0]

        # Verify workspace matches
        if employee.get('workspaces', {}).get('slug') != workspace_slug:
            raise HTTPException(
                status_code=400,
                detail=f"Employee {employee_email} is not in workspace {workspace_slug}"
            )

        # Generate Claude Desktop token
        result = await employee_auth._create_claude_desktop_token(employee["id"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Claude Desktop token generation failed: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    import os
    port = int(os.getenv("APP_PORT", 8003))
    uvicorn.run(app, host="0.0.0.0", port=port)
