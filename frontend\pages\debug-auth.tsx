// Debug page to test auth service responses
import { useState } from 'react';
import { apiService } from '../utils/api';

export default function DebugAuth() {
  const [authMeResult, setAuthMeResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAuthMe = async () => {
    setLoading(true);
    setError('');
    setAuthMeResult(null);

    try {
      console.log('🔍 Testing /auth/me endpoint...');
      
      // Get the token
      const token = apiService.getToken();
      console.log('🔑 Current token:', {
        hasToken: !!token,
        tokenLength: token?.length,
        tokenPreview: token ? `${token.substring(0, 20)}...` : 'null'
      });

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Make direct fetch call to auth/me
      const response = await fetch('http://localhost:8003/auth/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Response status:', response.status);
      
      const responseText = await response.text();
      console.log('📄 Raw response:', responseText);

      let result;
      try {
        result = JSON.parse(responseText);
        console.log('📊 Parsed response:', result);
      } catch (parseError) {
        throw new Error(`Failed to parse JSON: ${responseText}`);
      }

      setAuthMeResult(result);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Error:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const testGetCurrentUser = async () => {
    setLoading(true);
    setError('');
    setAuthMeResult(null);

    try {
      console.log('🔍 Testing apiService.getCurrentUser()...');
      const result = await apiService.getCurrentUser();
      console.log('✅ getCurrentUser result:', result);
      setAuthMeResult(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Error:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <h1 className="text-2xl font-bold mb-6">Auth Debug Page</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testAuthMe}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded mr-4"
        >
          {loading ? 'Testing...' : 'Test /auth/me Direct'}
        </button>
        
        <button
          onClick={testGetCurrentUser}
          disabled={loading}
          className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
        >
          {loading ? 'Testing...' : 'Test apiService.getCurrentUser()'}
        </button>
      </div>

      {error && (
        <div className="bg-red-900 border border-red-500 p-4 rounded mb-4">
          <h3 className="font-bold text-red-300">Error:</h3>
          <p className="text-red-200">{error}</p>
        </div>
      )}

      {authMeResult && (
        <div className="bg-gray-800 p-4 rounded">
          <h3 className="font-bold mb-2">Result:</h3>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(authMeResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
