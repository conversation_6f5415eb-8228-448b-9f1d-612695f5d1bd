#!/usr/bin/env node

// Uru Platform Claude Desktop MCP Server
// Standalone version for Claude Desktop configuration
// This file can be downloaded and used independently

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} = require('@modelcontextprotocol/sdk/types.js');

// Use axios if available, fallback to node-fetch, then to built-in fetch
let httpClient;
try {
  httpClient = require('axios');
} catch (e) {
  try {
    const fetch = require('node-fetch');
    httpClient = {
      post: async (url, data, config) => {
        const response = await fetch(url, {
          method: 'POST',
          headers: config?.headers || {},
          body: JSON.stringify(data),
          timeout: config?.timeout || 60000
        });
        return {
          data: await response.json(),
          status: response.status,
          statusText: response.statusText
        };
      },
      get: async (url, config) => {
        const response = await fetch(url, {
          method: 'GET',
          headers: config?.headers || {},
          timeout: config?.timeout || 30000
        });
        return {
          data: await response.json(),
          status: response.status,
          statusText: response.statusText
        };
      }
    };
  } catch (e2) {
    // Use built-in fetch (Node 18+)
    httpClient = {
      post: async (url, data, config) => {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(config?.headers || {})
          },
          body: JSON.stringify(data),
          signal: config?.timeout ? AbortSignal.timeout(config.timeout) : undefined
        });
        return {
          data: await response.json(),
          status: response.status,
          statusText: response.statusText
        };
      },
      get: async (url, config) => {
        const response = await fetch(url, {
          method: 'GET',
          headers: config?.headers || {},
          signal: config?.timeout ? AbortSignal.timeout(config.timeout) : undefined
        });
        return {
          data: await response.json(),
          status: response.status,
          statusText: response.statusText
        };
      }
    };
  }
}

class UruClaudeDesktopServer {
    constructor() {
        this.proxyUrl = process.env.MCP_PROXY_URL || 'https://mcp.uruenterprises.com';
        this.employeeToken = process.env.EMPLOYEE_TOKEN || '';
        this.debugMode = process.env.DEBUG_MODE === 'true';
        
        // Tool list caching
        this.toolsCache = {
            data: null,
            lastFetch: null,
            ttl: 30000 // Cache for 30 seconds
        };
        
        this.log('🔗 Uru Platform Claude Desktop MCP Server initializing...');
        this.log(`📡 Proxy URL: ${this.proxyUrl}`);
        this.log(`🔑 Employee Token: ${this.employeeToken ? this.employeeToken.substring(0, 20) + '...' : 'Not provided'}`);
        
        if (!this.employeeToken) {
            console.error('❌ EMPLOYEE_TOKEN environment variable is required');
            console.error('💡 Make sure to set the EMPLOYEE_TOKEN in your Claude Desktop MCP configuration');
            process.exit(1);
        }

        // Validate proxy URL
        try {
            new URL(this.proxyUrl);
        } catch (error) {
            console.error(`❌ Invalid MCP_PROXY_URL: ${this.proxyUrl}`);
            console.error('💡 Make sure MCP_PROXY_URL is a valid URL (e.g., https://mcp.uruenterprises.com)');
            process.exit(1);
        }
        
        this.server = new Server(
            {
                name: 'uru-platform-claude',
                version: '2.1.0',
            },
            {
                capabilities: {
                    tools: {},
                },
            }
        );
        
        this.setupHandlers();
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[DEBUG] ${message}`);
        }
    }

    async getCachedToolList() {
        const now = Date.now();
        
        // Return cached data if still valid
        if (this.toolsCache.data && 
            this.toolsCache.lastFetch && 
            (now - this.toolsCache.lastFetch) < this.toolsCache.ttl) {
            this.log('📋 Using cached tool list');
            return this.toolsCache.data;
        }
        
        try {
            this.log('📋 Fetching fresh tool list from proxy...');
            
            const response = await httpClient.get(`${this.proxyUrl}/mcp/tools/list`, {
                headers: {
                    'Authorization': `Bearer ${this.employeeToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });
            
            if (response.status === 200) {
                this.toolsCache.data = response.data;
                this.toolsCache.lastFetch = now;
                this.log(`✅ Cached ${response.data.tools?.length || 0} tools`);
                return response.data;
            } else {
                this.log(`⚠️ Tool list request failed: ${response.status} ${response.statusText}`);
                return { success: false, tools: [] };
            }
        } catch (error) {
            this.log(`❌ Error fetching tools: ${error.message}`);
            return { success: false, tools: [] };
        }
    }

    setupHandlers() {
        // Handle tool listing
        this.server.setRequestHandler(ListToolsRequestSchema, async (request) => {
            try {
                this.log('📋 Claude Desktop requesting tool list...');
                
                const response = await this.getCachedToolList();
                
                if (!response.success || !response.tools) {
                    this.log('⚠️ No tools available from proxy');
                    return { tools: [] };
                }
                
                // Convert proxy format to MCP format
                const mcpTools = response.tools.map(tool => ({
                    name: tool.function.name,
                    description: tool.function.description,
                    inputSchema: tool.function.parameters
                }));
                
                this.log(`✅ Returning ${mcpTools.length} tools to Claude Desktop`);
                
                return {
                    tools: mcpTools
                };
            } catch (error) {
                this.log(`❌ Error in tool listing: ${error.message}`);
                return { tools: [] };
            }
        });

        // Handle tool execution
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            try {
                const { name, arguments: toolArgs } = request.params;
                
                this.log(`🔧 Claude Desktop executing tool: ${name}`);
                this.log(`📝 Arguments: ${JSON.stringify(toolArgs, null, 2)}`);
                
                // Call proxy with MCP format
                const mcpRequest = {
                    jsonrpc: '2.0',
                    id: Date.now(),
                    method: 'tools/call',
                    params: {
                        name: name,
                        arguments: toolArgs || {}
                    }
                };
                
                const response = await httpClient.post(`${this.proxyUrl}/mcp/tools/call`, mcpRequest, {
                    headers: {
                        'Authorization': `Bearer ${this.employeeToken}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 60000 // 60 second timeout for tool execution
                });
                
                if (response.status === 200) {
                    const result = response.data;
                    this.log(`✅ Tool execution successful: ${name}`);
                    
                    // Return in MCP format
                    return {
                        content: [
                            {
                                type: "text",
                                text: typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2)
                            }
                        ]
                    };
                } else {
                    this.log(`❌ Tool execution failed: ${response.status} ${response.statusText}`);
                    return {
                        content: [
                            {
                                type: "text",
                                text: `Error: Tool execution failed with status ${response.status}`
                            }
                        ],
                        isError: true
                    };
                }
            } catch (error) {
                this.log(`❌ Error executing tool ${request.params.name}: ${error.message}`);
                return {
                    content: [
                        {
                            type: "text",
                            text: `Error executing tool: ${error.message}`
                        }
                    ],
                    isError: true
                };
            }
        });
    }

    async testConnection() {
        this.log('🔍 Testing connection to MCP proxy...');
        try {
            const response = await httpClient.get(`${this.proxyUrl}/health`, {
                headers: {
                    'Authorization': `Bearer ${this.employeeToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.status === 200) {
                this.log('✅ MCP proxy connection successful');
                return true;
            } else {
                this.log(`⚠️ MCP proxy returned status: ${response.status}`);
                return false;
            }
        } catch (error) {
            this.log(`❌ MCP proxy connection failed: ${error.message}`);
            console.error('💡 Check that:');
            console.error('   - MCP_PROXY_URL is correct');
            console.error('   - EMPLOYEE_TOKEN is valid');
            console.error('   - Network connection is available');
            return false;
        }
    }

    async start() {
        this.log('🚀 Starting Uru Platform Claude Desktop MCP Server...');

        // Test connection to proxy
        const connectionOk = await this.testConnection();
        if (!connectionOk) {
            console.error('❌ Failed to connect to MCP proxy. Server will still start but may not work properly.');
        }

        const transport = new StdioServerTransport();
        await this.server.connect(transport);

        console.log('✅ Uru Claude MCP Server ready for Claude Desktop!');
        console.log('🎯 Available: All company tools + personal tools (when OAuth connected)');
        if (!this.debugMode) {
            console.log('📱 Production mode: Clean responses for Claude Desktop');
        }
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down Uru Claude MCP Server...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Shutting down Uru Claude MCP Server...');
    process.exit(0);
});

// Handle uncaught errors
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Start the server
const server = new UruClaudeDesktopServer();
server.start().catch((error) => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
});
